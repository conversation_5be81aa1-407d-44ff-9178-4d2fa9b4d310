import { Component, <PERSON>Ini<PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-contact-popup',
  templateUrl: './contact-popup.component.html',
  styleUrls: ['./contact-popup.component.css'],
  standalone: false,
  animations: [
    trigger('slideInOut', [
      state('in', style({
        transform: 'translateX(0) scale(1)',
        opacity: 1
      })),
      state('out', style({
        transform: 'translateX(100%) scale(0.8)',
        opacity: 0
      })),
      transition('out => in', [
        style({ transform: 'translateX(100%) scale(0.8)', opacity: 0 }),
        animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({ transform: 'translateX(0) scale(1)', opacity: 1 }))
      ]),
      transition('in => out', [
        animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({ transform: 'translateX(100%) scale(0.8)', opacity: 0 }))
      ])
    ]),
    trigger('fadeInOut', [
      state('in', style({
        opacity: 1,
        transform: 'scale(1)'
      })),
      state('out', style({
        opacity: 0,
        transform: 'scale(0.8)'
      })),
      transition('out => in', [
        style({ opacity: 0, transform: 'scale(0.8)' }),
        animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({ opacity: 1, transform: 'scale(1)' }))
      ]),
      transition('in => out', [
        animate('200ms cubic-bezier(0.4, 0.0, 0.2, 1)', style({ opacity: 0, transform: 'scale(0.8)' }))
      ])
    ]),
    trigger('bounceIn', [
      transition(':enter', [
        style({ transform: 'scale(0)', opacity: 0 }),
        animate('600ms cubic-bezier(0.68, -0.55, 0.265, 1.55)', style({ transform: 'scale(1)', opacity: 1 }))
      ])
    ])
  ]
})
export class ContactPopupComponent implements OnInit, OnDestroy {
  isVisible = true; // Icon is always visible
  isExpanded = false;
  contactForm: FormGroup;
  isSubmitting = false;
  submitMessage = '';
  submitError = '';
  hasAutoOpened = false; // Track if auto-open has happened
  showPreOpenAnimation = false; // For pre-open animation

  private autoOpenTimer?: number;
  private preAnimationTimer?: number;

  constructor(private fb: FormBuilder) {
    this.contactForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      phone: ['', [Validators.required, Validators.pattern(/^\+?[0-9\s\-\(\)]{10,}$/)]]
    });
  }

  ngOnInit() {
    // Auto-open after 2 seconds, but only on desktop
    if (this.isDesktop()) {
      // Start pre-animation at 1.5 seconds
      this.preAnimationTimer = window.setTimeout(() => {
        this.showPreOpenAnimation = true;
      }, 1500);

      // Auto-open at 2 seconds
      this.autoOpenTimer = window.setTimeout(() => {
        if (!this.hasAutoOpened) {
          this.isExpanded = true;
          this.hasAutoOpened = true;
          this.showPreOpenAnimation = false;
        }
      }, 2000);
    }
  }

  ngOnDestroy() {
    if (this.autoOpenTimer) {
      clearTimeout(this.autoOpenTimer);
    }
    if (this.preAnimationTimer) {
      clearTimeout(this.preAnimationTimer);
    }
  }

  togglePopup() {
    this.isExpanded = !this.isExpanded;
    if (this.isExpanded) {
      this.resetForm();
    }
  }

  closePopup() {
    this.isExpanded = false; // Only close the expanded form, keep icon visible
  }

  private isDesktop(): boolean {
    return window.innerWidth >= 1024; // Desktop breakpoint (lg in Tailwind)
  }

  minimizePopup() {
    this.isExpanded = false;
  }

  onSubmit() {
    if (this.contactForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      this.submitError = '';
      this.submitMessage = '';

      // Simulate API call
      setTimeout(() => {
        this.isSubmitting = false;
        this.submitMessage = 'Спасибо! Мы свяжемся с вами в ближайшее время.';

        // Auto close after success, but keep icon visible
        setTimeout(() => {
          this.isExpanded = false;
        }, 2000);
      }, 1000);
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched() {
    Object.keys(this.contactForm.controls).forEach(key => {
      const control = this.contactForm.get(key);
      control?.markAsTouched();
    });
  }

  private resetForm() {
    this.contactForm.reset();
    this.submitMessage = '';
    this.submitError = '';
    this.isSubmitting = false;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.contactForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.contactForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return 'Это поле обязательно для заполнения';
      }
      if (field.errors['minlength']) {
        return 'Минимум 2 символа';
      }
      if (field.errors['pattern']) {
        return 'Введите корректный номер телефона';
      }
    }
    return '';
  }
}
