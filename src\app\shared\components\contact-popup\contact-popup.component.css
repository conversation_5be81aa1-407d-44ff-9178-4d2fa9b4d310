/* Contact Popup Styles */
.contact-popup {
  font-family: inherit;
}

/* Custom animations for smooth transitions */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Pulse animation for the operator icon */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Hover effects */
.contact-popup button:hover {
  transform: translateY(-1px);
}

/* Focus styles for accessibility */
.contact-popup input:focus {
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
}

/* Custom scrollbar for form if needed */
.contact-popup::-webkit-scrollbar {
  width: 4px;
}

.contact-popup::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.contact-popup::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.contact-popup::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .contact-popup {
    width: calc(100vw - 2rem);
    max-width: 320px;
  }
}

/* Animation states */
.slide-in {
  animation: slideInFromRight 0.3s ease-out forwards;
}

.slide-out {
  animation: slideOutToRight 0.3s ease-in forwards;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Tooltip styles */
.tooltip {
  position: relative;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  margin-bottom: 0.25rem;
}

.tooltip:hover::before {
  opacity: 1;
}

/* Form validation styles */
.form-field.error input {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Success message styles */
.success-message {
  background-color: #dcfce7;
  border-color: #16a34a;
  color: #15803d;
}

/* Button states */
.btn-primary {
  background-color: #7c3aed;
  transition: all 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  background-color: #6d28d9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Icon animations */
.icon-phone {
  transition: transform 0.2s ease;
}

.icon-phone:hover {
  transform: scale(1.1);
}

/* Backdrop blur effect */
.backdrop-blur {
  backdrop-filter: blur(4px);
}
