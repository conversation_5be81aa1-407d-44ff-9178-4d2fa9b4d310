import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-contact-popup',
  templateUrl: './contact-popup.component.html',
  styleUrls: ['./contact-popup.component.css'],
  standalone: false,
  animations: [
    trigger('slideInOut', [
      state('in', style({
        transform: 'translateX(0)',
        opacity: 1
      })),
      state('out', style({
        transform: 'translateX(100%)',
        opacity: 0
      })),
      transition('out => in', [
        animate('300ms ease-in-out')
      ]),
      transition('in => out', [
        animate('300ms ease-in-out')
      ])
    ]),
    trigger('fadeInOut', [
      state('in', style({ opacity: 1 })),
      state('out', style({ opacity: 0 })),
      transition('out => in', animate('200ms ease-in')),
      transition('in => out', animate('200ms ease-out'))
    ])
  ]
})
export class ContactPopupComponent implements OnInit, OnDestroy {
  isVisible = false;
  isExpanded = false;
  contactForm: FormGroup;
  isSubmitting = false;
  submitMessage = '';
  submitError = '';

  private showTimer?: number;

  constructor(private fb: FormBuilder) {
    this.contactForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      phone: ['', [Validators.required, Validators.pattern(/^\+?[0-9\s\-\(\)]{10,}$/)]]
    });
  }

  ngOnInit() {
    // Show popup after 3 seconds
    this.showTimer = window.setTimeout(() => {
      this.isVisible = true;
    }, 3000);
  }

  ngOnDestroy() {
    if (this.showTimer) {
      clearTimeout(this.showTimer);
    }
  }

  togglePopup() {
    this.isExpanded = !this.isExpanded;
    if (this.isExpanded) {
      this.resetForm();
    }
  }

  closePopup() {
    this.isVisible = false;
  }

  minimizePopup() {
    this.isExpanded = false;
  }

  onSubmit() {
    if (this.contactForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      this.submitError = '';
      this.submitMessage = '';

      // Simulate API call
      setTimeout(() => {
        this.isSubmitting = false;
        this.submitMessage = 'Спасибо! Мы свяжемся с вами в ближайшее время.';
        
        // Auto close after success
        setTimeout(() => {
          this.closePopup();
        }, 2000);
      }, 1000);
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched() {
    Object.keys(this.contactForm.controls).forEach(key => {
      const control = this.contactForm.get(key);
      control?.markAsTouched();
    });
  }

  private resetForm() {
    this.contactForm.reset();
    this.submitMessage = '';
    this.submitError = '';
    this.isSubmitting = false;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.contactForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.contactForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return 'Это поле обязательно для заполнения';
      }
      if (field.errors['minlength']) {
        return 'Минимум 2 символа';
      }
      if (field.errors['pattern']) {
        return 'Введите корректный номер телефона';
      }
    }
    return '';
  }
}
